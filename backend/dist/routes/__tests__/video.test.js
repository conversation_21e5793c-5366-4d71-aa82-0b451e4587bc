import { jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import videoRoutes from '../video';
import { videoService } from '../../services/videoService';
// Mock video service
jest.mock('../../services/videoService');
const mockVideoService = videoService;
describe('Video Routes', () => {
    let app;
    beforeEach(() => {
        // Create Express app for testing
        app = express();
        app.use(express.json());
        app.use('/video', videoRoutes);
        jest.clearAllMocks();
    });
    describe('POST /video/generate', () => {
        const mockTutorialPlan = {
            title: 'Test Tutorial',
            difficulty: 'beginner',
            estimatedTime: '10 minutes',
            technologies: ['JavaScript'],
            steps: [
                {
                    stepNumber: 1,
                    title: 'Step 1',
                    description: 'Test step',
                    uiActions: ['Click button'],
                    expectedResult: 'Button clicked'
                }
            ],
            prerequisites: ['Basic JavaScript'],
            resources: ['MDN Docs']
        };
        it('should generate video successfully', async () => {
            const mockVideoId = 'video_123_abc';
            mockVideoService.generateVideo.mockResolvedValue(mockVideoId);
            const response = await request(app)
                .post('/video/generate')
                .send({ tutorialPlan: mockTutorialPlan })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.videoId).toBe(mockVideoId);
            expect(response.body.status).toBe('processing');
            expect(response.body.message).toContain('started');
            expect(mockVideoService.generateVideo).toHaveBeenCalledWith(mockTutorialPlan, {});
        });
        it('should generate video with custom options', async () => {
            const mockVideoId = 'video_123_abc';
            mockVideoService.generateVideo.mockResolvedValue(mockVideoId);
            const options = {
                width: 1280,
                height: 720,
                fps: 24,
                quality: 'high'
            };
            const response = await request(app)
                .post('/video/generate')
                .send({
                tutorialPlan: mockTutorialPlan,
                options
            })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(mockVideoService.generateVideo).toHaveBeenCalledWith(mockTutorialPlan, options);
        });
        it('should return validation error for missing tutorial plan', async () => {
            const response = await request(app)
                .post('/video/generate')
                .send({})
                .expect(400);
            expect(response.body.error).toContain('Tutorial plan is required');
        });
        it('should return validation error for invalid tutorial plan', async () => {
            const response = await request(app)
                .post('/video/generate')
                .send({ tutorialPlan: 'invalid' })
                .expect(400);
            expect(response.body.error).toContain('Tutorial plan must be an object');
        });
        it('should handle video service errors', async () => {
            mockVideoService.generateVideo.mockRejectedValue(new Error('Video generation failed'));
            const response = await request(app)
                .post('/video/generate')
                .send({ tutorialPlan: mockTutorialPlan })
                .expect(500);
            expect(response.body.error).toBe('Failed to generate video');
        });
    });
    describe('GET /video/status/:videoId', () => {
        it('should return video status for existing video', async () => {
            const mockJob = {
                id: 'video_123_abc',
                status: 'completed',
                progress: 100,
                filePath: '/path/to/video.mp4',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            mockVideoService.getJobStatus.mockReturnValue(mockJob);
            const response = await request(app)
                .get('/video/status/video_123_abc')
                .expect(200);
            expect(response.body.videoId).toBe('video_123_abc');
            expect(response.body.status).toBe('completed');
            expect(response.body.progress).toBe(100);
            expect(response.body.downloadUrl).toBe('/api/video/download/video_123_abc');
        });
        it('should return 404 for non-existent video', async () => {
            mockVideoService.getJobStatus.mockReturnValue(null);
            const response = await request(app)
                .get('/video/status/non_existent')
                .expect(404);
            expect(response.body.error).toBe('Video not found');
        });
        it('should return status without download URL for processing video', async () => {
            const mockJob = {
                id: 'video_123_abc',
                status: 'processing',
                progress: 50,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            mockVideoService.getJobStatus.mockReturnValue(mockJob);
            const response = await request(app)
                .get('/video/status/video_123_abc')
                .expect(200);
            expect(response.body.status).toBe('processing');
            expect(response.body.progress).toBe(50);
            expect(response.body.downloadUrl).toBeUndefined();
        });
    });
    describe('GET /video/download/:videoId', () => {
        it('should download video file for completed video', async () => {
            const mockBuffer = Buffer.from('fake video data');
            mockVideoService.getVideoFile.mockResolvedValue(mockBuffer);
            const response = await request(app)
                .get('/video/download/video_123_abc')
                .expect(200);
            expect(response.headers['content-type']).toBe('video/mp4');
            expect(response.headers['content-disposition']).toContain('attachment');
            expect(Buffer.from(response.body)).toEqual(mockBuffer);
        });
        it('should return 404 for non-existent video file', async () => {
            mockVideoService.getVideoFile.mockResolvedValue(null);
            const response = await request(app)
                .get('/video/download/non_existent')
                .expect(404);
            expect(response.body.error).toBe('Video file not found');
        });
        it('should handle video service errors during download', async () => {
            mockVideoService.getVideoFile.mockRejectedValue(new Error('File read error'));
            const response = await request(app)
                .get('/video/download/video_123_abc')
                .expect(500);
            expect(response.body.error).toBe('Failed to download video');
        });
    });
    describe('DELETE /video/:videoId', () => {
        it('should delete video successfully', async () => {
            mockVideoService.deleteVideo.mockResolvedValue(true);
            const response = await request(app)
                .delete('/video/video_123_abc')
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.message).toContain('deleted');
            expect(mockVideoService.deleteVideo).toHaveBeenCalledWith('video_123_abc');
        });
        it('should return 404 for non-existent video', async () => {
            mockVideoService.deleteVideo.mockResolvedValue(false);
            const response = await request(app)
                .delete('/video/non_existent')
                .expect(404);
            expect(response.body.error).toBe('Video not found');
        });
        it('should handle video service errors during deletion', async () => {
            mockVideoService.deleteVideo.mockRejectedValue(new Error('Deletion failed'));
            const response = await request(app)
                .delete('/video/video_123_abc')
                .expect(500);
            expect(response.body.error).toBe('Failed to delete video');
        });
    });
    describe('GET /video/list', () => {
        it('should return list of videos', async () => {
            const mockJobs = [
                {
                    id: 'video_1',
                    status: 'completed',
                    progress: 100,
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    id: 'video_2',
                    status: 'processing',
                    progress: 50,
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            ];
            mockVideoService.listJobs.mockReturnValue(mockJobs);
            const response = await request(app)
                .get('/video/list')
                .expect(200);
            expect(response.body.videos).toHaveLength(2);
            expect(response.body.videos[0].videoId).toBe('video_1');
            expect(response.body.videos[1].videoId).toBe('video_2');
        });
        it('should return empty list when no videos exist', async () => {
            mockVideoService.listJobs.mockReturnValue([]);
            const response = await request(app)
                .get('/video/list')
                .expect(200);
            expect(response.body.videos).toHaveLength(0);
        });
    });
});
