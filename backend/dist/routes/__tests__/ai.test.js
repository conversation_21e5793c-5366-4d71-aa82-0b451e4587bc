import { jest } from '@jest/globals';
import request from 'supertest';
import express from 'express';
import { GoogleGenerativeAI } from '@google/generative-ai';
import aiRoutes from '../ai';
// Mock Google AI
jest.mock('@google/generative-ai');
const MockGoogleGenerativeAI = GoogleGenerativeAI;
describe('AI Routes', () => {
    let app;
    let mockModel;
    beforeEach(() => {
        // Create Express app for testing
        app = express();
        app.use(express.json());
        app.use('/ai', aiRoutes);
        // Mock Google AI model
        mockModel = {
            generateContent: jest.fn()
        };
        const mockGenAI = {
            getGenerativeModel: jest.fn().mockReturnValue(mockModel)
        };
        MockGoogleGenerativeAI.mockImplementation(() => mockGenAI);
        jest.clearAllMocks();
    });
    describe('POST /ai/analyze', () => {
        it('should analyze problem and return tutorial plan', async () => {
            const mockResponse = {
                response: {
                    text: () => JSON.stringify({
                        title: 'Test Tutorial',
                        difficulty: 'beginner',
                        estimatedTime: '10 minutes',
                        technologies: ['JavaScript'],
                        steps: [
                            {
                                stepNumber: 1,
                                title: 'Step 1',
                                description: 'Test step',
                                codeExample: 'console.log("test")',
                                uiActions: ['Open console'],
                                expectedResult: 'Console opens'
                            }
                        ],
                        prerequisites: ['Basic JavaScript'],
                        resources: ['MDN Docs']
                    })
                }
            };
            mockModel.generateContent.mockResolvedValue(mockResponse);
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to create a React component?' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan).toBeDefined();
            expect(response.body.tutorialPlan.title).toBe('Test Tutorial');
            expect(response.body.tutorialPlan.steps).toHaveLength(1);
            expect(response.body.metadata.model).toBe('gemini-pro');
        });
        it('should return validation error for empty problem', async () => {
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: '' })
                .expect(400);
            expect(response.body.error).toContain('required');
        });
        it('should return validation error for missing problem', async () => {
            const response = await request(app)
                .post('/ai/analyze')
                .send({})
                .expect(400);
            expect(response.body.error).toContain('required');
        });
        it('should handle Google AI API errors gracefully', async () => {
            mockModel.generateContent.mockRejectedValue(new Error('API Error'));
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to create a React component?' })
                .expect(200);
            // Should fall back to mock response
            expect(response.body.success).toBe(true);
            expect(response.body.metadata.model).toBe('mock');
        });
        it('should handle malformed JSON response from AI', async () => {
            const mockResponse = {
                response: {
                    text: () => 'This is not valid JSON'
                }
            };
            mockModel.generateContent.mockResolvedValue(mockResponse);
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to create a React component?' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan.title).toBe('Custom Tutorial');
            expect(response.body.tutorialPlan.steps[0].description).toContain('This is not valid JSON');
        });
        it('should generate mock tutorial for React component', async () => {
            // Test without API key to trigger mock generation
            const originalApiKey = process.env.GOOGLE_AI_API_KEY;
            process.env.GOOGLE_AI_API_KEY = '';
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to create a React component?' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan.title).toBe('Creating a React Component');
            expect(response.body.tutorialPlan.difficulty).toBe('beginner');
            expect(response.body.tutorialPlan.technologies).toContain('React');
            expect(response.body.metadata.model).toBe('mock');
            // Restore API key
            process.env.GOOGLE_AI_API_KEY = originalApiKey;
        });
        it('should generate mock tutorial for authentication', async () => {
            const originalApiKey = process.env.GOOGLE_AI_API_KEY;
            process.env.GOOGLE_AI_API_KEY = '';
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'I want to add authentication to my React app' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan.title).toBe('Add Authentication to React App');
            expect(response.body.tutorialPlan.difficulty).toBe('intermediate');
            expect(response.body.tutorialPlan.technologies).toContain('Firebase');
            process.env.GOOGLE_AI_API_KEY = originalApiKey;
        });
        it('should generate mock tutorial for database connection', async () => {
            const originalApiKey = process.env.GOOGLE_AI_API_KEY;
            process.env.GOOGLE_AI_API_KEY = '';
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to connect database to my frontend?' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan.title).toBe('Connect Database to Frontend');
            expect(response.body.tutorialPlan.difficulty).toBe('intermediate');
            expect(response.body.tutorialPlan.technologies).toContain('MongoDB');
            process.env.GOOGLE_AI_API_KEY = originalApiKey;
        });
        it('should generate mock tutorial for deployment', async () => {
            const originalApiKey = process.env.GOOGLE_AI_API_KEY;
            process.env.GOOGLE_AI_API_KEY = '';
            const response = await request(app)
                .post('/ai/analyze')
                .send({ problem: 'How to deploy my React app to Vercel?' })
                .expect(200);
            expect(response.body.success).toBe(true);
            expect(response.body.tutorialPlan.title).toBe('Deploy React App to Vercel');
            expect(response.body.tutorialPlan.difficulty).toBe('beginner');
            expect(response.body.tutorialPlan.technologies).toContain('Vercel');
            process.env.GOOGLE_AI_API_KEY = originalApiKey;
        });
    });
    describe('GET /ai/examples', () => {
        it('should return tutorial examples', async () => {
            const response = await request(app)
                .get('/ai/examples')
                .expect(200);
            expect(response.body.examples).toBeDefined();
            expect(Array.isArray(response.body.examples)).toBe(true);
            expect(response.body.examples.length).toBeGreaterThan(0);
            const firstExample = response.body.examples[0];
            expect(firstExample).toHaveProperty('id');
            expect(firstExample).toHaveProperty('title');
            expect(firstExample).toHaveProperty('problem');
            expect(firstExample).toHaveProperty('difficulty');
            expect(firstExample).toHaveProperty('estimatedTime');
        });
        it('should return examples with correct structure', async () => {
            const response = await request(app)
                .get('/ai/examples')
                .expect(200);
            response.body.examples.forEach((example) => {
                expect(typeof example.id).toBe('number');
                expect(typeof example.title).toBe('string');
                expect(typeof example.problem).toBe('string');
                expect(['beginner', 'intermediate', 'advanced']).toContain(example.difficulty);
                expect(typeof example.estimatedTime).toBe('string');
            });
        });
    });
});
