import express from 'express';
import { videoService } from '../services/videoService.js';
const router = express.Router();
// Generate video from tutorial plan
router.post('/generate', async (req, res) => {
    try {
        const { tutorialPlan, options = {} } = req.body;
        if (!tutorialPlan) {
            return res.status(400).json({
                error: 'Tutorial plan is required'
            });
        }
        // Validate tutorial plan structure
        if (!tutorialPlan.title || !tutorialPlan.steps || !Array.isArray(tutorialPlan.steps)) {
            return res.status(400).json({
                error: 'Invalid tutorial plan structure'
            });
        }
        console.log(`🎬 Starting video generation for: ${tutorialPlan.title}`);
        console.log(`📊 Steps: ${tutorialPlan.steps.length}`);
        // Start video generation using Remotion
        const videoId = await videoService.generateVideo(tutorialPlan, options);
        // Calculate estimated completion time based on video complexity
        const stepDuration = 8; // seconds per step
        const renderTimePerSecond = 2; // seconds of render time per second of video
        const totalVideoSeconds = 3 + (tutorialPlan.steps.length * stepDuration); // intro + steps
        const estimatedRenderTime = totalVideoSeconds * renderTimePerSecond * 1000; // in milliseconds
        const estimatedCompletionTime = new Date(Date.now() + estimatedRenderTime).toISOString();
        res.json({
            success: true,
            videoId,
            status: 'processing',
            estimatedCompletionTime,
            message: 'Video generation started using Remotion',
            metadata: {
                title: tutorialPlan.title,
                duration: `${Math.ceil(totalVideoSeconds / 60)}:${(totalVideoSeconds % 60).toString().padStart(2, '0')}`,
                steps: tutorialPlan.steps.length,
                createdAt: new Date().toISOString(),
                difficulty: tutorialPlan.difficulty,
                technologies: tutorialPlan.technologies
            }
        });
    }
    catch (error) {
        console.error('Video Generation Error:', error);
        res.status(500).json({
            error: 'Failed to generate video',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get video status
router.get('/status/:videoId', (req, res) => {
    const { videoId } = req.params;
    const job = videoService.getJobStatus(videoId);
    if (!job) {
        return res.status(404).json({
            error: 'Video not found',
            videoId
        });
    }
    res.json({
        videoId: job.id,
        status: job.status,
        progress: job.progress,
        downloadUrl: job.status === 'completed' ? `/api/video/download/${videoId}` : undefined,
        previewUrl: job.status === 'completed' ? `/api/video/preview/${videoId}` : undefined,
        updatedAt: job.updatedAt.toISOString(),
        error: job.error
    });
});
// Download video
router.get('/download/:videoId', async (req, res) => {
    try {
        const { videoId } = req.params;
        const job = videoService.getJobStatus(videoId);
        if (!job) {
            return res.status(404).json({
                error: 'Video not found'
            });
        }
        if (job.status !== 'completed') {
            return res.status(400).json({
                error: 'Video not ready for download',
                status: job.status,
                progress: job.progress
            });
        }
        const videoBuffer = await videoService.getVideoFile(videoId);
        if (!videoBuffer) {
            return res.status(404).json({
                error: 'Video file not found'
            });
        }
        // Set appropriate headers for video download
        res.setHeader('Content-Type', 'video/mp4');
        res.setHeader('Content-Disposition', `attachment; filename="${videoId}.mp4"`);
        res.setHeader('Content-Length', videoBuffer.length);
        res.send(videoBuffer);
    }
    catch (error) {
        console.error('Video download error:', error);
        res.status(500).json({
            error: 'Failed to download video',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// Get video preview/thumbnail
router.get('/preview/:videoId', (req, res) => {
    const { videoId } = req.params;
    res.json({
        message: 'Video preview endpoint',
        videoId,
        thumbnailUrl: 'https://via.placeholder.com/800x450/3b82f6/ffffff?text=CodeTutor+AI+Video',
        note: 'This will serve actual video thumbnails once Remotion integration is complete'
    });
});
// List user's videos
router.get('/list', (req, res) => {
    try {
        const jobs = videoService.listJobs();
        const videos = jobs.map(job => ({
            id: job.id,
            status: job.status,
            progress: job.progress,
            createdAt: job.createdAt.toISOString(),
            updatedAt: job.updatedAt.toISOString(),
            error: job.error,
            downloadUrl: job.status === 'completed' ? `/api/video/download/${job.id}` : undefined
        }));
        res.json({
            videos,
            total: videos.length
        });
    }
    catch (error) {
        console.error('List videos error:', error);
        res.status(500).json({
            error: 'Failed to list videos',
            message: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
export default router;
