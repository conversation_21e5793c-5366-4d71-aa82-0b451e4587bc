import dotenv from 'dotenv';
// Load test environment variables
dotenv.config({ path: '.env.test' });
dotenv.config({ path: '.env' });
// Mock console methods to reduce noise in tests
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.GOOGLE_AI_API_KEY = 'test-api-key';
process.env.PORT = '3001';
// Global test timeout
jest.setTimeout(10000);
