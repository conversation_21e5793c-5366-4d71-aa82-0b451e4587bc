describe('Basic Test Setup', () => {
    it('should run a basic test', () => {
        expect(1 + 1).toBe(2);
    });
    it('should have access to environment variables', () => {
        expect(process.env.NODE_ENV).toBe('test');
    });
    it('should be able to mock functions', () => {
        const mockFn = jest.fn();
        mockFn('test');
        expect(mockFn).toHaveBeenCalledWith('test');
    });
});
export {};
