import { jest } from '@jest/globals';
import { exec } from 'child_process';
import fs from 'fs/promises';
import { videoService } from '../videoService';
// Mock dependencies
jest.mock('child_process');
jest.mock('fs/promises');
const mockExec = exec;
const mockFs = fs;
describe('VideoService', () => {
    const mockTutorialPlan = {
        title: 'Test Tutorial',
        difficulty: 'beginner',
        estimatedTime: '10 minutes',
        technologies: ['JavaScript', 'React'],
        steps: [
            {
                stepNumber: 1,
                title: 'Step 1',
                description: 'First step description',
                codeExample: 'console.log("Hello World")',
                uiActions: ['Open terminal', 'Type command'],
                expectedResult: 'Hello World appears in console'
            },
            {
                stepNumber: 2,
                title: 'Step 2',
                description: 'Second step description',
                uiActions: ['Click button'],
                expectedResult: 'Button is clicked'
            }
        ],
        prerequisites: ['Node.js installed'],
        resources: ['MDN Documentation']
    };
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock fs.mkdir to resolve successfully
        mockFs.mkdir.mockResolvedValue(undefined);
        // Mock fs.writeFile to resolve successfully
        mockFs.writeFile.mockResolvedValue(undefined);
        // Mock fs.access to resolve successfully (file exists)
        mockFs.access.mockResolvedValue(undefined);
        // Mock fs.unlink to resolve successfully
        mockFs.unlink.mockResolvedValue(undefined);
    });
    describe('generateVideo', () => {
        it('should generate video successfully', async () => {
            // Mock successful exec
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Video generated successfully', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            expect(videoId).toBeDefined();
            expect(videoId).toMatch(/^video_\d+_[a-z0-9]+$/);
            // Wait a bit for async processing
            await new Promise(resolve => setTimeout(resolve, 100));
            const job = videoService.getJobStatus(videoId);
            expect(job).toBeDefined();
            expect(job?.status).toBe('processing');
        });
        it('should handle video generation with custom options', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Video generated successfully', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const options = {
                width: 1280,
                height: 720,
                fps: 24,
                quality: 'high'
            };
            const videoId = await videoService.generateVideo(mockTutorialPlan, options);
            expect(videoId).toBeDefined();
            // Wait for processing
            await new Promise(resolve => setTimeout(resolve, 100));
            const job = videoService.getJobStatus(videoId);
            expect(job).toBeDefined();
        });
        it('should handle video generation failure', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(new Error('Remotion render failed'), null);
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            expect(videoId).toBeDefined();
            // Wait for processing to complete
            await new Promise(resolve => setTimeout(resolve, 200));
            const job = videoService.getJobStatus(videoId);
            expect(job?.status).toBe('error');
            expect(job?.error).toContain('Remotion render failed');
        });
    });
    describe('getJobStatus', () => {
        it('should return job status for existing job', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Success', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            const job = videoService.getJobStatus(videoId);
            expect(job).toBeDefined();
            expect(job?.id).toBe(videoId);
            expect(job?.status).toBe('processing');
            expect(job?.progress).toBe(0);
        });
        it('should return null for non-existent job', () => {
            const job = videoService.getJobStatus('non-existent-id');
            expect(job).toBeNull();
        });
    });
    describe('getVideoFile', () => {
        it('should return video file for completed job', async () => {
            const mockBuffer = Buffer.from('fake video data');
            mockFs.readFile.mockResolvedValue(mockBuffer);
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Success', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            // Manually set job as completed for testing
            const job = videoService.getJobStatus(videoId);
            if (job) {
                job.status = 'completed';
                job.filePath = '/fake/path/video.mp4';
            }
            const videoFile = await videoService.getVideoFile(videoId);
            expect(videoFile).toEqual(mockBuffer);
        });
        it('should return null for non-completed job', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Success', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            const videoFile = await videoService.getVideoFile(videoId);
            expect(videoFile).toBeNull();
        });
    });
    describe('deleteVideo', () => {
        it('should delete video successfully', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Success', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId = await videoService.generateVideo(mockTutorialPlan);
            // Set job as completed with file path
            const job = videoService.getJobStatus(videoId);
            if (job) {
                job.status = 'completed';
                job.filePath = '/fake/path/video.mp4';
            }
            const result = await videoService.deleteVideo(videoId);
            expect(result).toBe(true);
            expect(mockFs.unlink).toHaveBeenCalledWith('/fake/path/video.mp4');
        });
        it('should return false for non-existent video', async () => {
            const result = await videoService.deleteVideo('non-existent-id');
            expect(result).toBe(false);
        });
    });
    describe('listJobs', () => {
        it('should return list of all jobs', async () => {
            const mockCallback = jest.fn((callback) => {
                callback(null, { stdout: 'Success', stderr: '' });
            });
            mockExec.mockImplementation(mockCallback);
            const videoId1 = await videoService.generateVideo(mockTutorialPlan);
            const videoId2 = await videoService.generateVideo(mockTutorialPlan);
            const jobs = videoService.listJobs();
            expect(jobs).toHaveLength(2);
            expect(jobs.map(job => job.id)).toContain(videoId1);
            expect(jobs.map(job => job.id)).toContain(videoId2);
        });
    });
});
