import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs/promises';
const execAsync = promisify(exec);
class VideoService {
    jobs = new Map();
    outputDir;
    constructor() {
        // Use a path without spaces to avoid command line issues
        this.outputDir = path.join(process.cwd(), '..', 'videos');
        this.ensureOutputDir();
    }
    async ensureOutputDir() {
        try {
            await fs.mkdir(this.outputDir, { recursive: true });
        }
        catch (error) {
            console.error('Failed to create output directory:', error);
        }
    }
    async generateVideo(tutorialPlan, options = {}) {
        const videoId = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const outputPath = path.join(this.outputDir, `${videoId}.mp4`);
        // Create job entry
        const job = {
            id: videoId,
            status: 'processing',
            progress: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        this.jobs.set(videoId, job);
        // Start video generation in background
        this.processVideo(videoId, tutorialPlan, outputPath, options)
            .catch(error => {
            console.error(`Video generation failed for ${videoId}:`, error);
            this.updateJob(videoId, {
                status: 'error',
                error: error.message,
                progress: 0
            });
        });
        return videoId;
    }
    async processVideo(videoId, tutorialPlan, outputPath, options) {
        try {
            // Update progress
            this.updateJob(videoId, { progress: 10 });
            // Calculate video duration based on steps
            const stepDuration = 8; // seconds per step
            const introDuration = 3; // intro duration
            const totalDuration = introDuration + (tutorialPlan.steps.length * stepDuration);
            const totalFrames = totalDuration * (options.fps || 30);
            // Update progress
            this.updateJob(videoId, { progress: 20 });
            // Create props file for Remotion
            const propsPath = path.join(this.outputDir, `${videoId}_props.json`);
            const props = {
                title: tutorialPlan.title,
                steps: tutorialPlan.steps,
                metadata: {
                    difficulty: tutorialPlan.difficulty,
                    estimatedTime: tutorialPlan.estimatedTime,
                    technologies: tutorialPlan.technologies
                }
            };
            await fs.writeFile(propsPath, JSON.stringify(props, null, 2));
            this.updateJob(videoId, { progress: 30 });
            // Build Remotion command
            const animationEnginePath = path.join(process.cwd(), '..', 'animation-engine');
            const width = options.width || 1920;
            const height = options.height || 1080;
            const fps = options.fps || 30;
            const command = [
                'npx remotion render',
                'TutorialVideo',
                `"${outputPath}"`, // Quote the path to handle spaces
                `--props="${propsPath}"`, // Quote the props path
                `--width=${width}`,
                `--height=${height}`,
                `--fps=${fps}`,
                `--frames=0-${totalFrames - 1}`, // Use frame range instead of single frame
                '--overwrite'
            ].join(' ');
            console.log(`🎬 Starting video render for ${videoId}`);
            console.log(`Command: ${command}`);
            console.log(`Working directory: ${animationEnginePath}`);
            this.updateJob(videoId, { progress: 40 });
            // Execute Remotion render
            const { stdout, stderr } = await execAsync(command, {
                cwd: animationEnginePath,
                maxBuffer: 1024 * 1024 * 10 // 10MB buffer
            });
            console.log(`✅ Video render completed for ${videoId}`);
            if (stdout)
                console.log('Stdout:', stdout);
            if (stderr)
                console.log('Stderr:', stderr);
            // Verify output file exists
            try {
                await fs.access(outputPath);
                this.updateJob(videoId, {
                    status: 'completed',
                    progress: 100,
                    filePath: outputPath
                });
            }
            catch (error) {
                throw new Error(`Output file not found: ${outputPath}`);
            }
            // Clean up props file
            try {
                await fs.unlink(propsPath);
            }
            catch (error) {
                console.warn('Failed to clean up props file:', error);
            }
        }
        catch (error) {
            console.error(`❌ Video generation failed for ${videoId}:`, error);
            this.updateJob(videoId, {
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
                progress: 0
            });
            throw error;
        }
    }
    updateJob(videoId, updates) {
        const job = this.jobs.get(videoId);
        if (job) {
            Object.assign(job, updates, { updatedAt: new Date() });
            this.jobs.set(videoId, job);
        }
    }
    getJobStatus(videoId) {
        return this.jobs.get(videoId) || null;
    }
    async getVideoFile(videoId) {
        const job = this.jobs.get(videoId);
        if (!job || job.status !== 'completed' || !job.filePath) {
            return null;
        }
        try {
            return await fs.readFile(job.filePath);
        }
        catch (error) {
            console.error(`Failed to read video file for ${videoId}:`, error);
            return null;
        }
    }
    async deleteVideo(videoId) {
        const job = this.jobs.get(videoId);
        if (!job)
            return false;
        try {
            if (job.filePath) {
                await fs.unlink(job.filePath);
            }
            this.jobs.delete(videoId);
            return true;
        }
        catch (error) {
            console.error(`Failed to delete video ${videoId}:`, error);
            return false;
        }
    }
    listJobs() {
        return Array.from(this.jobs.values());
    }
}
// Create singleton instance
export const videoService = new VideoService();
export default VideoService;
