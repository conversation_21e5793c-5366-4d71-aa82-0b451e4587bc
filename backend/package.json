{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "description": "Backend API for CodeTutor AI", "dependencies": {"@google/generative-ai": "^0.21.0", "@tailwindcss/postcss": "^4.1.11", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/node": "^24.0.11", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}