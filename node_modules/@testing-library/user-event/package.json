{"name": "@testing-library/user-event", "version": "14.6.1", "description": "Fire events the same way the user does", "keywords": ["react-testing-library", "dom-testing-library", "react", "testing"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=12", "npm": ">=6"}, "repository": {"type": "git", "url": "https://github.com/testing-library/user-event"}, "bugs": {"url": "https://github.com/testing-library/user-event/issues"}, "homepage": "https://github.com/testing-library/user-event#readme", "files": ["dist"], "scripts": {"build": "scripts ts-build2 --cjs --target es2019", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "setup:env": "node --experimental-import-meta-resolve scripts/setup.js", "test": "kcd-scripts test", "test:jest": "kcd-scripts test", "test:toolbox": "NODE_OPTIONS='--experimental-vm-modules --experimental-modules --experimental-import-meta-resolve' node scripts/test.js", "test:debug": "kcd-scripts --inspect-brk test --runInBand", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts typecheck"}, "devDependencies": {"@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@ph.fritsche/scripts-config": "^2.4.0", "@ph.fritsche/toolbox": "^1.0.0-alpha.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^5.16.3", "@testing-library/react": "^16.1.0", "@types/jest-in-case": "^1.0.3", "@types/react": "^18.0.25", "@types/sinonjs__fake-timers": "^8.1.2", "css.escape": "^1.5.1", "esbuild": "^0.19.10", "esbuild-plugin-globals": "^0.2.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-local-rules": "^1.3.2", "expect": "^28.1.3", "is-ci": "^3.0.1", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "jest-in-case": "^1.0.2", "jest-mock": "^28.1.3", "jest-serializer-ansi": "^1.0.3", "jsdom": "^20.0.3", "kcd-scripts": "^12.1.0", "react": "^18.0.0", "react-dom": "^18.0.0", "shared-scripts": "^1.5.1", "ts-node": "^10.9.1", "typescript": "^4.9.3"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}, "dependencies": {}, "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/cjs/index.js", "default": "./dist/esm/index.js"}, "./dist/cjs/*.js": {"types": "./dist/types/*.d.ts", "import": "./dist/esm/*.js", "default": "./dist/cjs/*.js"}, "./dist/esm/*.js": {"types": "./dist/types/*.d.ts", "default": "./dist/esm/*.js", "require": "./dist/cjs/*.js"}}, "typesVersions": {"*": {"dist/types/*": ["./dist/types/*"], "dist/cjs/*.js": ["./dist/types/*.d.ts"], "dist/esm/*.js": ["./dist/types/*.d.ts"], "*": ["./dist/types/*.d.ts"]}}}