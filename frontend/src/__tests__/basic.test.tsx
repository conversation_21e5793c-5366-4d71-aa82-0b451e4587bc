import React from 'react'
import { render } from '@testing-library/react'

describe('Basic Frontend Test Setup', () => {
  it('should run a basic test', () => {
    expect(1 + 1).toBe(2)
  })

  it('should render a simple React component', () => {
    const TestComponent = () => <div>Hello Test</div>
    const { getByText } = render(<TestComponent />)
    expect(getByText('Hello Test')).toBeInTheDocument()
  })

  it('should be able to mock functions', () => {
    const mockFn = jest.fn()
    mockFn('test')
    expect(mockFn).toHaveBeenCalledWith('test')
  })
})
