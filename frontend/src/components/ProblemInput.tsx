import React, { useState } from 'react'

interface ProblemInputProps {
  onSubmit?: (problem: string) => void
}

const ProblemInput: React.FC<ProblemInputProps> = ({ onSubmit }) => {
  const [problem, setProblem] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  console.log('🔄 ProblemInput render - problem:', problem, 'isLoading:', isLoading, 'onSubmit:', !!onSubmit)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('📝 ProblemInput: Form submitted with problem:', problem)
    console.log('📝 ProblemInput: onSubmit callback exists:', !!onSubmit)

    if (!problem.trim()) {
      console.warn('⚠️ ProblemInput: Empty problem, returning early')
      return
    }

    console.log('⏳ ProblemInput: Setting loading state and calling onSubmit')
    setIsLoading(true)
    try {
      if (onSubmit) {
        console.log('🔄 ProblemInput: Calling onSubmit callback with problem:', problem)
        await onSubmit(problem)
        console.log('✅ ProblemInput: onSubmit completed successfully')
      } else {
        console.error('❌ ProblemInput: No onSubmit callback provided!')
        alert('ERROR: No onSubmit callback provided to ProblemInput component')
      }
    } catch (error) {
      console.error('❌ ProblemInput: Error submitting problem:', error)
    } finally {
      console.log('🏁 ProblemInput: Setting loading to false')
      setIsLoading(false)
    }
  }

  const exampleProblems = [
    "I want to add Supabase authentication to my React app",
    "How do I set up Google OAuth in my Next.js project",
    "I need to connect my database to my frontend",
    "How do I deploy my React app to Vercel"
  ]

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Describe Your Coding Problem
      </h3>

      {/* Emergency Test Button */}
      <button
        onClick={() => {
          console.log('🚨 EMERGENCY TEST BUTTON CLICKED!')
          alert('Emergency test button works!')
        }}
        className="mb-4 bg-red-500 text-white px-4 py-2 rounded"
      >
        🚨 EMERGENCY TEST - Click Me!
      </button>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="problem" className="block text-sm font-medium text-gray-700 mb-2">
            What coding challenge are you facing?
          </label>
          <textarea
            id="problem"
            value={problem}
            onChange={(e) => {
              console.log('📝 Textarea changed:', e.target.value)
              setProblem(e.target.value)
            }}
            placeholder="Example: I want to add user authentication to my React app using Firebase..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
            rows={4}
            disabled={isLoading}
          />
        </div>

        <button
          type="button"
          disabled={!problem.trim() || isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          onClick={(e) => {
            console.log('🖱️ Generate Tutorial Button clicked! Problem:', problem, 'Disabled:', !problem.trim() || isLoading, 'onSubmit exists:', !!onSubmit)
            e.preventDefault()
            handleSubmit(e as any)
          }}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Generating Tutorial...
            </div>
          ) : (
            `Generate Tutorial ${!problem.trim() ? '(Enter text first)' : ''}`
          )}
        </button>

        {/* Debug button */}
        <button
          type="button"
          onClick={() => {
            console.log('🧪 Debug button clicked!')
            alert(`Debug: Problem = "${problem}", Length = ${problem.length}, Trimmed = "${problem.trim()}"`)
          }}
          className="w-full mt-2 bg-gray-500 text-white py-1 px-2 rounded text-sm"
        >
          Debug: Test Button Click
        </button>

        {/* Test Generate Button */}
        <button
          type="button"
          onClick={() => {
            console.log('🔥 TEST GENERATE BUTTON CLICKED!')
            alert('TEST GENERATE BUTTON WORKS!')
            if (onSubmit) {
              console.log('🔥 Calling onSubmit directly')
              onSubmit(problem)
            } else {
              console.log('🔥 NO ONSUBMIT CALLBACK!')
            }
          }}
          className="w-full mt-2 bg-red-500 text-white py-2 px-4 rounded text-sm"
        >
          🔥 TEST GENERATE (RED BUTTON)
        </button>
      </form>

      <div className="mt-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3">
          Try these examples:
        </h4>
        <div className="space-y-2">
          {exampleProblems.map((example, index) => (
            <button
              key={index}
              onClick={() => setProblem(example)}
              className="w-full text-left p-3 text-sm text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
              disabled={isLoading}
            >
              {example}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default ProblemInput
