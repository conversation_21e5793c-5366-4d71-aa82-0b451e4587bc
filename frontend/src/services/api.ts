// API service for communicating with the CodeTutor AI backend

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001'

// Custom error classes for better error handling
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class NetworkError extends Error {
  constructor(message: string = 'Network connection failed') {
    super(message)
    this.name = 'NetworkError'
  }
}

export class TimeoutError extends Error {
  constructor(message: string = 'Request timed out') {
    super(message)
    this.name = 'TimeoutError'
  }
}

// Types for API responses
export interface TutorialStep {
  stepNumber: number
  title: string
  description: string
  codeExample?: string
  uiActions: string[]
  expectedResult: string
}

export interface TutorialPlan {
  title: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: string
  technologies: string[]
  steps: TutorialStep[]
  prerequisites: string[]
  resources: string[]
}

export interface AnalyzeResponse {
  success: boolean
  tutorialPlan: TutorialPlan
  metadata: {
    generatedAt: string
    model: string
    originalProblem: string
  }
}

export interface VideoGenerationResponse {
  success: boolean
  videoId: string
  status: 'processing' | 'completed' | 'error'
  estimatedCompletionTime: string
  message: string
  metadata: {
    title: string
    duration: string
    steps: number
    createdAt: string
  }
}

export interface VideoStatus {
  videoId: string
  status: 'processing' | 'completed' | 'error'
  progress: number
  downloadUrl?: string
  previewUrl?: string
  updatedAt: string
}

export interface ExampleProblem {
  id: number
  title: string
  problem: string
  difficulty: string
  estimatedTime: string
}

// API service class
class ApiService {
  private baseUrl: string

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    retries: number = 2
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      timeout: 30000, // 30 second timeout
      ...options,
    }

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 30000)

        const response = await fetch(url, {
          ...config,
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}))

          // Don't retry client errors (4xx)
          if (response.status >= 400 && response.status < 500) {
            throw new ApiError(
              errorData.message || `Client error: ${response.status}`,
              response.status,
              errorData.code,
              errorData
            )
          }

          // Retry server errors (5xx) if we have attempts left
          if (attempt < retries) {
            await this.delay(1000 * (attempt + 1)) // Exponential backoff
            continue
          }

          throw new ApiError(
            errorData.message || `Server error: ${response.status}`,
            response.status,
            errorData.code,
            errorData
          )
        }

        return await response.json()
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          throw new TimeoutError(`Request to ${endpoint} timed out`)
        }

        if (error instanceof TypeError && error.message.includes('fetch')) {
          if (attempt < retries) {
            await this.delay(1000 * (attempt + 1))
            continue
          }
          throw new NetworkError(`Failed to connect to ${this.baseUrl}`)
        }

        if (error instanceof ApiError || error instanceof TimeoutError || error instanceof NetworkError) {
          throw error
        }

        console.error(`API request failed: ${endpoint}`, error)
        throw new ApiError(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    throw new ApiError(`Failed after ${retries + 1} attempts`)
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // AI endpoints
  async analyzeProblem(problem: string): Promise<AnalyzeResponse> {
    console.log('🔍 API Service: Analyzing problem:', problem)
    console.log('🌐 API Base URL:', this.baseUrl)

    try {
      const result = await this.request<AnalyzeResponse>('/api/ai/analyze', {
        method: 'POST',
        body: JSON.stringify({ problem }),
      })
      console.log('✅ API Service: Analysis successful:', result)
      return result
    } catch (error) {
      console.error('❌ API Service: Analysis failed:', error)
      throw error
    }
  }

  async getExamples(): Promise<{ examples: ExampleProblem[] }> {
    return this.request<{ examples: ExampleProblem[] }>('/api/ai/examples')
  }

  // Video endpoints
  async generateVideo(
    tutorialPlan: TutorialPlan, 
    options: Record<string, any> = {}
  ): Promise<VideoGenerationResponse> {
    return this.request<VideoGenerationResponse>('/api/video/generate', {
      method: 'POST',
      body: JSON.stringify({ tutorialPlan, options }),
    })
  }

  async getVideoStatus(videoId: string): Promise<VideoStatus> {
    return this.request<VideoStatus>(`/api/video/status/${videoId}`)
  }

  async downloadVideo(videoId: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/api/video/download/${videoId}`)
    if (!response.ok) {
      throw new Error(`Failed to download video: ${response.status}`)
    }
    return response.blob()
  }

  async getVideoPreview(videoId: string): Promise<{ thumbnailUrl: string }> {
    return this.request<{ thumbnailUrl: string }>(`/api/video/preview/${videoId}`)
  }

  async listVideos(): Promise<{ videos: any[], total: number }> {
    return this.request<{ videos: any[], total: number }>('/api/video/list')
  }

  // Health check
  async healthCheck(): Promise<{ status: string, timestamp: string, version: string }> {
    return this.request<{ status: string, timestamp: string, version: string }>('/api/health')
  }
}

// Create and export a singleton instance
export const apiService = new ApiService()

// Export the class for testing or custom instances
export default ApiService
