import React, { useState } from 'react'
import ProblemInput from './components/ProblemInput'
import ProgressTracker from './components/ProgressTracker'
import VideoPlayer from './components/VideoPlayer'
import ErrorDisplay from './components/ErrorDisplay'
import { useTutorialGeneration } from './hooks/useTutorialGeneration'
import { apiService } from './services/api'

function App() {
  const [lastProblem, setLastProblem] = useState('')
  const [healthStatus, setHealthStatus] = useState('')
  const [isRetrying, setIsRetrying] = useState(false)

  const {
    isGenerating,
    tutorialPlan,
    videoStatus,
    steps,
    error,
    canRetry,
    generateTutorial,
    retryCurrentOperation,
    resetState,
    clearError
  } = useTutorialGeneration()

  const handleProblemSubmit = async (problem: string) => {
    console.log('🎯 App: Received problem submission:', problem)
    setLastProblem(problem)
    try {
      console.log('🚀 App: Calling generateTutorial')
      await generateTutorial(problem)
      console.log('✅ App: Tutorial generation completed')
    } catch (error) {
      console.error('❌ App: Failed to generate tutorial:', error)
      // Error is already handled by the hook
    }
  }

  const handleReset = () => {
    setLastProblem('')
    setIsRetrying(false)
    resetState()
  }

  const testApiConnection = async () => {
    try {
      console.log('🔍 Testing API connection...')
      setHealthStatus('Testing...')
      const health = await apiService.healthCheck()
      console.log('✅ API Health check successful:', health)
      setHealthStatus(`✅ Connected: ${health.status}`)
    } catch (error) {
      console.error('❌ API Health check failed:', error)
      setHealthStatus(`❌ Failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleRetry = async () => {
    if (!canRetry) return

    setIsRetrying(true)
    try {
      await retryCurrentOperation(lastProblem)
    } catch (error) {
      console.error('Retry failed:', error)
    } finally {
      setIsRetrying(false)
    }
  }

  const handleDismissError = () => {
    clearError()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                CodeTutor AI
              </h1>
              <span className="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                Beta
              </span>
            </div>
            <nav className="flex space-x-8 items-center">
              <a href="#" className="text-gray-500 hover:text-gray-900">
                How it works
              </a>
              <a href="#" className="text-gray-500 hover:text-gray-900">
                Examples
              </a>
              <button
                onClick={testApiConnection}
                className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"
              >
                Test API
              </button>
              {healthStatus && (
                <span className="text-xs text-gray-600">{healthStatus}</span>
              )}
              {(tutorialPlan || isGenerating) && (
                <button
                  onClick={handleReset}
                  className="text-gray-500 hover:text-gray-900"
                >
                  New Tutorial
                </button>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            AI-Powered Coding Tutorials
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Describe your coding problem and get a personalized animated tutorial
            showing exactly what steps to take, what buttons to click, and what code to write.
          </p>
        </div>

        {/* Enhanced Error Display */}
        {error && (
          <div className="mb-8 max-w-2xl mx-auto">
            <ErrorDisplay
              error={error}
              onRetry={canRetry ? handleRetry : undefined}
              onDismiss={handleDismissError}
              isRetrying={isRetrying}
              canRetry={canRetry}
            />
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Input Section */}
          <div className="space-y-6">
            <ProblemInput onSubmit={handleProblemSubmit} />
            <ProgressTracker steps={steps} />
          </div>

          {/* Video Section */}
          <div>
            <VideoPlayer
              videoUrl={videoStatus?.downloadUrl}
              isLoading={isGenerating}
            />
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
